// نظام المصادقة للوحة الإدارة

// الدالة الرئيسية لإدارة نظام تسجيل الدخول
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من وجود توكن تسجيل دخول سابق
    checkLoggedIn();
    
    // إضافة مستمع الحدث لنموذج تسجيل الدخول
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // إضافة مستمع الحدث لزر إظهار/إخفاء كلمة المرور
    const togglePasswordBtn = document.querySelector('.toggle-password');
    if (togglePasswordBtn) {
        togglePasswordBtn.addEventListener('click', togglePasswordVisibility);
    }
});

// التحقق مما إذا كان المستخدم مسجل دخوله بالفعل
function checkLoggedIn() {
    const token = localStorage.getItem('auth_token');
    const rememberMe = localStorage.getItem('remember_me') === 'true';
    
    // إذا لم يكن هناك توكن، فلا داعي للتحقق أكثر
    if (!token) return;
    
    // التحقق من صلاحية التوكن
    try {
        const payload = parseJwt(token);
        const currentTime = Math.floor(Date.now() / 1000);
        
        // التحقق من انتهاء صلاحية التوكن
        if (payload.exp < currentTime) {
            // حذف التوكن المنتهي
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            return;
        }
        
        // إذا كان المستخدم بالفعل مسجل دخوله وهو على صفحة تسجيل الدخول، يتم توجيهه للوحة التحكم
        if (window.location.pathname.includes('login.html')) {
            window.location.href = 'dashboard.html';
        }
    } catch (error) {
        console.error('خطأ في التحقق من التوكن:', error);
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
    }
}

// معالجة تسجيل الدخول
function handleLogin(event) {
    event.preventDefault();
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('rememberMe').checked;
    const errorDiv = document.getElementById('loginError');
    
    // إخفاء رسالة الخطأ السابقة إن وجدت
    errorDiv.classList.add('d-none');
    
    // البحث عن المستخدم في قاعدة البيانات
    const user = users.find(u => u.email === email && u.password === password);
    
    if (user) {
        // إنشاء توكن JWT
        const token = generateJWT(user);
        
        // حفظ التوكن وبيانات المستخدم
        localStorage.setItem('auth_token', token);
        localStorage.setItem('remember_me', rememberMe);
        
        // حفظ بيانات المستخدم (باستثناء كلمة المرور)
        const userData = {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role
        };
        localStorage.setItem('user_data', JSON.stringify(userData));
        
        // توجيه المستخدم إلى صفحة لوحة التحكم
        window.location.href = 'dashboard.html';
    } else {
        // عرض رسالة خطأ
        errorDiv.textContent = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
        errorDiv.classList.remove('d-none');
    }
}

// تبديل رؤية كلمة المرور
function togglePasswordVisibility() {
    const passwordInput = document.getElementById('password');
    const icon = document.querySelector('.toggle-password i');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

// تسجيل الخروج
function logout() {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
    
    // الحفاظ على خيار "تذكرني" إذا كان مفعلاً
    if (localStorage.getItem('remember_me') === 'true') {
        localStorage.removeItem('remember_me');
    }
    
    // توجيه المستخدم إلى صفحة تسجيل الدخول
    window.location.href = 'login.html';
}

// إنشاء توكن JWT بسيط
// ملاحظة: في الإنتاج، يجب أن يتم ذلك على الخادم وليس في جانب العميل
function generateJWT(user) {
    // إنشاء الرأس والحمولة
    const header = {
        alg: 'HS256',
        typ: 'JWT'
    };
    
    const payload = {
        sub: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60) // 30 يوم أو 1 يوم
    };
    
    // تحويل الرأس والحمولة إلى سلاسل مشفرة بـ Base64
    const encodedHeader = btoa(JSON.stringify(header));
    const encodedPayload = btoa(JSON.stringify(payload));
    
    // التوقيع (في الإنتاج، يتم ذلك باستخدام مفتاح سري آمن)
    const signature = btoa(`${encodedHeader}.${encodedPayload}`);
    
    // إنشاء التوكن الكامل
    return `${encodedHeader}.${encodedPayload}.${signature}`;
}

// تحليل توكن JWT
function parseJwt(token) {
    try {
        const base64Payload = token.split('.')[1];
        const payload = atob(base64Payload);
        return JSON.parse(payload);
    } catch (error) {
        console.error('خطأ في تحليل التوكن:', error);
        return null;
    }
}

// التحقق من صلاحيات المستخدم
function checkPermission(requiredRole) {
    const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
    const userRole = userData.role;
    
    // المدير لديه جميع الصلاحيات
    if (userRole === 'admin') return true;
    
    // التحقق من الدور المطلوب
    if (requiredRole === 'admin' && userRole !== 'admin') {
        return false;
    }
    
    return true;
}
