<!DOCTYPE html>
<html lang="ar" dir="rtl" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - إدارة متجر VelaSweets</title>
    <link rel="stylesheet" href="styles/main.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .dashboard-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .welcome-card {
            background-color: var(--primary-color);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .nav-item .active {
            background-color: var(--primary-color);
            color: white;
        }

        .logout-btn {
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="layout-container">
        <!-- الشريط الجانبي -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-brand">
                    <h1>Vela Sweets</h1>
                    <p>لوحة الإدارة</p>
                </div>
            </div>
            <ul class="sidebar-menu">
                <li class="sidebar-item">
                    <a href="dashboard.html" class="sidebar-link active">
                        <i class="bi bi-speedometer2"></i>
                        <span class="sidebar-link-text">لوحة القيادة</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="products.html" class="sidebar-link">
                        <i class="bi bi-box-seam"></i>
                        <span class="sidebar-link-text">المنتجات</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="discounts.html" class="sidebar-link">
                        <i class="bi bi-percent"></i>
                        <span class="sidebar-link-text">الخصومات</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="orders.html" class="sidebar-link">
                        <i class="bi bi-cart3"></i>
                        <span class="sidebar-link-text">الطلبات</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="tracking.html" class="sidebar-link">
                        <i class="bi bi-truck"></i>
                        <span class="sidebar-link-text">تتبع الطلبات والتوصيل</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="customers.html" class="sidebar-link">
                        <i class="bi bi-people"></i>
                        <span class="sidebar-link-text">الزبائن والرسائل</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="rewards.html" class="sidebar-link">
                        <i class="bi bi-star"></i>
                        <span class="sidebar-link-text">نقاط المكافآت</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="settings.html" class="sidebar-link">
                        <i class="bi bi-gear"></i>
                        <span class="sidebar-link-text">إعدادات المتجر</span>
                    </a>
                </li>
                <li class="sidebar-item">
                    <a href="logs.html" class="sidebar-link">
                        <i class="bi bi-activity"></i>
                        <span class="sidebar-link-text">سجل العمليات</span>
                    </a>
                </li>
            </ul>
            <div class="sidebar-footer">
                <div class="theme-toggle" id="themeToggle">
                    <i class="bi bi-moon"></i>
                    <span>الوضع الداكن</span>
                </div>
                <div class="user-info">
                    <span id="userNameDisplay"></span>
                </div>
            </div>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- شريط التنقل للجوال -->
            <div class="mobile-navbar d-lg-none">
                <button class="mobile-toggle" id="sidebarToggle">
                    <i class="bi bi-list"></i>
                </button>
                <div class="mobile-brand">Vela Sweets</div>
                <div class="user-dropdown">
                    <button class="user-dropdown-toggle" id="userDropdown">
                        <span id="mobileUserName"></span>
                        <i class="bi bi-person-circle"></i>
                    </button>
                    <div class="user-dropdown-menu" id="userMenu">
                        <a href="#" class="user-dropdown-item">
                            <i class="bi bi-person"></i> الملف الشخصي
                        </a>
                        <div class="user-dropdown-divider"></div>
                        <a href="#" class="user-dropdown-item" onclick="logout()">
                            <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>

            <!-- عنوان الصفحة -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">لوحة القيادة</h1>
                <div class="d-none d-lg-flex align-items-center">
                    <span id="dateDisplay" class="me-3"></span>
                    <a href="#" class="btn btn-outline-danger" onclick="logout()">
                        <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
                    </a>
                </div>
            </div>

            <!-- شريط الترحيب -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 id="welcomeMessage">أهلاً بك في نظام إدارة متجر VelaSweets</h2>
                            <p class="text-muted">هنا يمكنك إدارة متجرك بكفاءة وسهولة</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <img src="assets/images/logo-placeholder.png" alt="Vela Sweets Logo" height="60" class="img-fluid" onerror="this.src='https://placehold.co/200x60?text=Vela+Sweets'">
                        </div>
                    </div>
                </div>
            </div>

            <!-- بطاقات الإحصائيات -->
            <div class="row">
                <div class="col-md-3 col-sm-6 mb-4">
                    <div class="card stat-card">
                        <div class="stat-card-icon">
                            <i class="bi bi-currency-dollar"></i>
                        </div>
                        <h3 class="stat-card-title">إجمالي المبيعات</h3>
                        <div class="stat-card-value">0 د.ع</div>
                        <div class="stat-card-subtitle">آخر 30 يوم</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-4">
                    <div class="card stat-card">
                        <div class="stat-card-icon">
                            <i class="bi bi-cart-check"></i>
                        </div>
                        <h3 class="stat-card-title">إجمالي الطلبات</h3>
                        <div class="stat-card-value">0</div>
                        <div class="stat-card-subtitle">آخر 30 يوم</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-4">
                    <div class="card stat-card">
                        <div class="stat-card-icon">
                            <i class="bi bi-people"></i>
                        </div>
                        <h3 class="stat-card-title">إجمالي الزبائن</h3>
                        <div class="stat-card-value">0</div>
                        <div class="stat-card-subtitle">المسجلين</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 mb-4">
                    <div class="card stat-card">
                        <div class="stat-card-icon">
                            <i class="bi bi-eye"></i>
                        </div>
                        <h3 class="stat-card-title">الزوار</h3>
                        <div class="stat-card-value">0</div>
                        <div class="stat-card-subtitle">اليوم</div>
                    </div>
                </div>
            </div>

            <!-- أقسام إضافية -->
            <div class="row">
                <!-- الطلبات الأخيرة -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <span>آخر الطلبات</span>
                            <a href="orders.html" class="btn btn-sm btn-primary">عرض الكل</a>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                لا توجد طلبات بعد. ستظهر هنا عندما يبدأ العملاء بالطلب.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المنتجات الأكثر مبيعاً -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <span>المنتجات الأكثر مبيعاً</span>
                            <a href="products.html" class="btn btn-sm btn-primary">عرض الكل</a>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                لا توجد منتجات بعد. قم بإضافة منتجات لعرضها هنا.
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الروابط السريعة -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <span>روابط سريعة</span>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3 col-6 mb-3">
                                    <a href="products.html" class="btn btn-lg btn-light w-100 h-100 py-4">
                                        <i class="bi bi-plus-circle fs-2 d-block mb-2"></i>
                                        إضافة منتج
                                    </a>
                                </div>
                                <div class="col-md-3 col-6 mb-3">
                                    <a href="discounts.html" class="btn btn-lg btn-light w-100 h-100 py-4">
                                        <i class="bi bi-tags fs-2 d-block mb-2"></i>
                                        إنشاء خصم
                                    </a>
                                </div>
                                <div class="col-md-3 col-6 mb-3">
                                    <a href="settings.html" class="btn btn-lg btn-light w-100 h-100 py-4">
                                        <i class="bi bi-gear fs-2 d-block mb-2"></i>
                                        الإعدادات
                                    </a>
                                </div>
                                <div class="col-md-3 col-6 mb-3">
                                    <a href="tracking.html" class="btn btn-lg btn-light w-100 h-100 py-4">
                                        <i class="bi bi-truck fs-2 d-block mb-2"></i>
                                        تتبع الطلبات
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="database/users.js"></script>
    <script src="scripts/auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من وجود توكن صالح للمستخدم
            const token = localStorage.getItem('auth_token');
            if (!token) {
                // إذا لم يكن هناك توكن، يتم توجيه المستخدم إلى صفحة تسجيل الدخول
                window.location.href = 'login.html';
                return;
            }
            
            try {
                // استرداد بيانات المستخدم
                const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
                
                // عرض اسم المستخدم
                document.getElementById('userNameDisplay').textContent = userData.name || 'مستخدم';
                document.getElementById('mobileUserName').textContent = userData.name || 'مستخدم';
                
                // تخصيص رسالة الترحيب حسب دور المستخدم
                const welcomeMessage = document.getElementById('welcomeMessage');
                if (userData.role === 'admin') {
                    welcomeMessage.textContent = `أهلاً بك ${userData.name}، أنت مسجل دخولك كمدير للنظام`;
                } else {
                    welcomeMessage.textContent = `أهلاً بك ${userData.name}، أنت مسجل دخولك كموظف`;
                }
                
                // عرض التاريخ الحالي
                const dateOptions = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
                const today = new Date();
                document.getElementById('dateDisplay').textContent = today.toLocaleDateString('ar-IQ', dateOptions);
                
            } catch (error) {
                console.error('خطأ في استرداد بيانات المستخدم:', error);
            }

            // مستمع الحدث لتبديل الشريط الجانبي للجوال
            document.getElementById('sidebarToggle').addEventListener('click', function() {
                document.querySelector('.sidebar').classList.toggle('expanded');
            });

            // مستمع الحدث لتبديل قائمة المستخدم
            document.getElementById('userDropdown').addEventListener('click', function() {
                document.getElementById('userMenu').classList.toggle('show');
            });

            // مستمع الحدث لتبديل الوضع الداكن
            document.getElementById('themeToggle').addEventListener('click', function() {
                const htmlElement = document.documentElement;
                const currentTheme = htmlElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                
                htmlElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                
                // تحديث أيقونة التبديل
                const icon = this.querySelector('i');
                const text = this.querySelector('span');
                
                if (newTheme === 'dark') {
                    icon.classList.remove('bi-moon');
                    icon.classList.add('bi-sun');
                    text.textContent = 'الوضع الفاتح';
                } else {
                    icon.classList.remove('bi-sun');
                    icon.classList.add('bi-moon');
                    text.textContent = 'الوضع الداكن';
                }
            });

            // تحقق من الوضع المحفوظ
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                document.documentElement.setAttribute('data-theme', savedTheme);
                
                if (savedTheme === 'dark') {
                    const themeToggle = document.getElementById('themeToggle');
                    const icon = themeToggle.querySelector('i');
                    const text = themeToggle.querySelector('span');
                    
                    icon.classList.remove('bi-moon');
                    icon.classList.add('bi-sun');
                    text.textContent = 'الوضع الفاتح';
                }
            }

            // إغلاق قائمة المستخدم عند النقر خارجها
            document.addEventListener('click', function(event) {
                if (!event.target.closest('#userDropdown') && !event.target.closest('#userMenu')) {
                    document.getElementById('userMenu').classList.remove('show');
                }
            });
        });
    </script>
</body>
</html>
