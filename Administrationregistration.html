<!DOCTYPE html>
<html lang="ar" dir="rtl" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل المديرين - إدارة متجر VelaSweets</title>
    <link rel="stylesheet" href="styles/main.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .registration-container {
            max-width: 800px;
            margin: 30px auto;
            background-color: var(--card-bg);
            border-radius: 15px;
            box-shadow: 0 10px 30px var(--shadow-color);
            overflow: hidden;
        }
        
        .registration-header {
            background-color: var(--primary-color);
            color: white;
            padding: 25px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .registration-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
            opacity: 0.5;
            z-index: 0;
        }
        
        .registration-header h1 {
            position: relative;
            z-index: 1;
            font-family: 'Playfair Display', serif;
            margin-bottom: 5px;
        }
        
        .registration-header p {
            position: relative;
            z-index: 1;
            opacity: 0.9;
            margin: 0;
        }
        
        .registration-body {
            padding: 30px;
        }
        
        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-color);
        }
        
        .input-group-text {
            background-color: var(--primary-color);
            color: white;
            border: none;
        }
        
        .form-control {
            padding: 12px;
            border-radius: 8px;
        }
        
        .btn-register {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.3s;
            width: 100%;
            margin-top: 10px;
        }
        
        .btn-register:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
        }
        
        .form-section {
            margin-bottom: 30px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 20px;
        }
        
        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .password-info {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            background-color: rgba(var(--bs-info-rgb), 0.1);
        }
        
        .password-info ul {
            padding-right: 20px;
            margin-bottom: 0;
        }
        
        .account-summary {
            background-color: rgba(var(--bs-light-rgb), 0.5);
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            border: 1px solid var(--border-color);
        }
        
        .account-summary h4 {
            font-size: 1.1rem;
            margin-bottom: 15px;
            color: var(--primary-color);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 8px;
        }
        
        .account-detail {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .account-detail strong {
            color: var(--primary-color);
        }
        
        .back-link {
            display: block;
            text-align: center;
            margin-top: 20px;
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="registration-container">
        <div class="registration-header">
            <h1>تسجيل مدير جديد</h1>
            <p>إنشاء حساب إداري جديد للوصول إلى لوحة تحكم VelaSweets</p>
        </div>
        
        <div class="registration-body">
            <form id="adminRegistrationForm">
                <!-- معلومات المدير -->
                <div class="form-section">
                    <h3 class="mb-4">المعلومات الشخصية</h3>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="adminName" class="form-label">الاسم</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-person"></i></span>
                                <input type="text" class="form-control" id="adminName" name="adminName" required>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="adminRole" class="form-label">الدور الوظيفي</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-briefcase"></i></span>
                                <select class="form-select" id="adminRole" name="adminRole" required>
                                    <option value="">اختر الدور الوظيفي</option>
                                    <option value="admin">مدير النظام</option>
                                    <option value="employee">موظف</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات الحساب -->
                <div class="form-section">
                    <h3 class="mb-4">معلومات الحساب</h3>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="adminEmail" class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                <input type="email" class="form-control" id="adminEmail" name="adminEmail" required placeholder="<EMAIL>">
                            </div>
                            <small class="text-muted">البريد الإلكتروني يجب أن يكون بصيغة velasweets#@store.iq</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="adminPhone" class="form-label">رقم الهاتف</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-phone"></i></span>
                                <input type="tel" class="form-control" id="adminPhone" name="adminPhone" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="adminPassword" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                <input type="password" class="form-control" id="adminPassword" name="adminPassword" required>
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="far fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="confirmPassword" class="form-label">تأكيد كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-lock-fill"></i></span>
                                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="far fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="password-info">
                        <h5><i class="bi bi-info-circle me-2"></i> متطلبات كلمة المرور:</h5>
                        <ul>
                            <li>يجب أن تتكون من 8 أحرف على الأقل</li>
                            <li>يجب أن تحتوي على حرف كبير واحد على الأقل</li>
                            <li>يجب أن تحتوي على رقم واحد على الأقل</li>
                            <li>يجب أن تحتوي على رمز خاص واحد على الأقل (مثل !@#$%^&*)</li>
                        </ul>
                    </div>
                </div>
                
                <!-- ملخص الحساب -->
                <div class="account-summary">
                    <h4><i class="bi bi-card-checklist me-2"></i> ملخص معلومات الحساب</h4>
                    <div class="account-detail">
                        <span>الاسم:</span>
                        <strong id="summary-name">-</strong>
                    </div>
                    <div class="account-detail">
                        <span>البريد الإلكتروني:</span>
                        <strong id="summary-email">-</strong>
                    </div>
                    <div class="account-detail">
                        <span>الدور الوظيفي:</span>
                        <strong id="summary-role">-</strong>
                    </div>
                </div>
                
                <div class="alert alert-success mt-3 d-none" id="registrationSuccess">
                    تم إنشاء الحساب بنجاح! سيتم توجيهك إلى صفحة تسجيل الدخول...
                </div>
                
                <div class="alert alert-danger mt-3 d-none" id="registrationError"></div>
                
                <div class="mt-4">
                    <button type="submit" class="btn btn-register">
                        <i class="bi bi-person-plus me-2"></i> إنشاء حساب
                    </button>
                </div>
            </form>
            
            <a href="login.html" class="back-link">
                <i class="bi bi-arrow-left me-1"></i> العودة إلى صفحة تسجيل الدخول
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="database/users.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // مستمع الحدث لنموذج التسجيل
            const form = document.getElementById('adminRegistrationForm');
            if (form) {
                form.addEventListener('submit', handleRegistration);
            }
            
            // مستمع الحدث لأزرار إظهار/إخفاء كلمة المرور
            const togglePasswordButtons = document.querySelectorAll('.toggle-password');
            togglePasswordButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const input = this.previousElementSibling;
                    const icon = this.querySelector('i');
                    
                    if (input.type === 'password') {
                        input.type = 'text';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        input.type = 'password';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                });
            });
            
            // تحديث ملخص الحساب عند إدخال البيانات
            const nameInput = document.getElementById('adminName');
            const emailInput = document.getElementById('adminEmail');
            const roleSelect = document.getElementById('adminRole');
            
            nameInput.addEventListener('input', updateSummary);
            emailInput.addEventListener('input', updateSummary);
            roleSelect.addEventListener('change', updateSummary);
        });
        
        // تحديث ملخص الحساب
        function updateSummary() {
            const name = document.getElementById('adminName').value || '-';
            const email = document.getElementById('adminEmail').value || '-';
            const roleSelect = document.getElementById('adminRole');
            let role = '-';
            
            if (roleSelect.value === 'admin') {
                role = 'مدير النظام';
            } else if (roleSelect.value === 'employee') {
                role = 'موظف';
            }
            
            document.getElementById('summary-name').textContent = name;
            document.getElementById('summary-email').textContent = email;
            document.getElementById('summary-role').textContent = role;
        }
        
        // التحقق من صحة البريد الإلكتروني
        function validateEmail(email) {
            // التحقق من أن البريد يتبع النمط المطلوب
            const emailPattern = /^velasweets[0-9]*@store\.iq$/;
            return emailPattern.test(email);
        }
        
        // التحقق من قوة كلمة المرور
        function validatePassword(password) {
            // التحقق من أن كلمة المرور تفي بمتطلبات القوة
            const minLength = password.length >= 8;
            const hasUpperCase = /[A-Z]/.test(password);
            const hasNumber = /[0-9]/.test(password);
            const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
            
            const isValid = minLength && hasUpperCase && hasNumber && hasSpecial;
            const issues = [];
            
            if (!minLength) issues.push("يجب أن تتكون من 8 أحرف على الأقل");
            if (!hasUpperCase) issues.push("يجب أن تحتوي على حرف كبير واحد على الأقل");
            if (!hasNumber) issues.push("يجب أن تحتوي على رقم واحد على الأقل");
            if (!hasSpecial) issues.push("يجب أن تحتوي على رمز خاص واحد على الأقل");
            
            return {
                isValid,
                issues
            };
        }
        
        // معالجة عملية التسجيل
        function handleRegistration(event) {
            event.preventDefault();
            
            const name = document.getElementById('adminName').value;
            const email = document.getElementById('adminEmail').value;
            const role = document.getElementById('adminRole').value;
            const password = document.getElementById('adminPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const phone = document.getElementById('adminPhone').value;
            
            const errorDiv = document.getElementById('registrationError');
            const successDiv = document.getElementById('registrationSuccess');
            
            // إخفاء رسائل الخطأ والنجاح السابقة
            errorDiv.classList.add('d-none');
            successDiv.classList.add('d-none');
            
            // التحقق من تعبئة جميع الحقول
            if (!name || !email || !role || !password || !confirmPassword || !phone) {
                errorDiv.textContent = 'يرجى تعبئة جميع الحقول المطلوبة';
                errorDiv.classList.remove('d-none');
                return;
            }
            
            // التحقق من تطابق كلمات المرور
            if (password !== confirmPassword) {
                errorDiv.textContent = 'كلمات المرور غير متطابقة';
                errorDiv.classList.remove('d-none');
                return;
            }
            
            // التحقق من صحة البريد الإلكتروني
            if (!validateEmail(email)) {
                errorDiv.textContent = 'صيغة البريد الإلكتروني غير صحيحة، يجب أن تكون بصيغة velasweets#@store.iq';
                errorDiv.classList.remove('d-none');
                return;
            }
            
            // التحقق من قوة كلمة المرور
            const passwordValidation = validatePassword(password);
            if (!passwordValidation.isValid) {
                errorDiv.innerHTML = 'كلمة المرور لا تفي بمتطلبات القوة:<ul>' + 
                    passwordValidation.issues.map(issue => `<li>${issue}</li>`).join('') + 
                    '</ul>';
                errorDiv.classList.remove('d-none');
                return;
            }
            
            // التحقق من عدم وجود بريد إلكتروني مكرر
            if (typeof users !== 'undefined') {
                const existingUser = users.find(u => u.email === email);
                if (existingUser) {
                    errorDiv.textContent = 'هذا البريد الإلكتروني مستخدم بالفعل';
                    errorDiv.classList.remove('d-none');
                    return;
                }
            }
            
            // إنشاء معرف فريد للمستخدم الجديد
            const newUserId = typeof users !== 'undefined' ? users.length + 1 : 1;
            
            // إنشاء كائن المستخدم الجديد
            const newUser = {
                id: newUserId,
                name: name,
                email: email,
                password: password,
                role: role,
                phone: phone
            };
            
            // إضافة المستخدم إلى قاعدة البيانات (في سيناريو حقيقي، سيتم إرسال البيانات إلى الخادم)
            if (typeof users !== 'undefined') {
                users.push(newUser);
                
                // عرض رسالة النجاح
                successDiv.classList.remove('d-none');
                
                // إعادة تعيين النموذج
                form.reset();
                
                // توجيه المستخدم إلى صفحة تسجيل الدخول بعد فترة قصيرة
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 3000);
            } else {
                // في حالة عدم وجود مصفوفة المستخدمين (عند الاختبار المحلي)
                console.log('تم إنشاء المستخدم (محاكاة):', newUser);
                
                // عرض رسالة النجاح
                successDiv.classList.remove('d-none');
                
                // إعادة تعيين النموذج
                document.getElementById('adminRegistrationForm').reset();
                
                // تحديث الملخص
                updateSummary();
            }
        }
    </script>
</body>
</html> 