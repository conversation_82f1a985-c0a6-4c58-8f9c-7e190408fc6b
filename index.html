<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>متجر VelaSweets - نظام الإدارة</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f8f9fa;
            font-family: 'Cairo', sans-serif;
        }
        
        .loading {
            text-align: center;
        }
        
        .loading h2 {
            color: #5a287d;
        }
        
        .spinner {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #5a287d;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="loading">
        <h2>متجر VelaSweets</h2>
        <p>جاري تحميل نظام الإدارة...</p>
        <div class="spinner"></div>
    </div>

    <script>
        // التحقق من وجود مستخدم مسجل دخوله
        window.onload = function() {
            setTimeout(function() {
                const token = localStorage.getItem('auth_token');
                if (token) {
                    // إذا كان هناك توكن، توجيه المستخدم إلى لوحة التحكم
                    window.location.href = 'dashboard.html';
                } else {
                    // إذا لم يكن هناك توكن، توجيه المستخدم إلى صفحة تسجيل الدخول
                    window.location.href = 'login.html';
                }
            }, 1500); // انتظار 1.5 ثانية للتأثير المرئي
        };
    </script>
</body>
</html>
