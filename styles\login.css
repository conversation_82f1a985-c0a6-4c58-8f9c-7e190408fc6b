body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.login-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    padding: 30px;
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h2 {
    color: #5a287d;
    margin-bottom: 5px;
}

.login-header p {
    color: #888;
    font-size: 16px;
}

.login-form {
    margin-top: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.btn-primary {
    background-color: #5a287d;
    border-color: #5a287d;
    padding: 10px;
    font-size: 16px;
    transition: all 0.3s;
}

.btn-primary:hover {
    background-color: #4a1d6d;
    border-color: #4a1d6d;
}

.toggle-password {
    cursor: pointer;
}

@media (max-width: 576px) {
    .login-container {
        padding: 20px;
        margin: 0 15px;
    }
} 